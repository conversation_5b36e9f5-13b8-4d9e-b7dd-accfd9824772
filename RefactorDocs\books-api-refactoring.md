# Refactoring Plan for `books-api.ts`

This document provides a detailed analysis of the `electron/main/api/books-api.ts` file and proposes a refactoring plan to improve its structure, performance, and maintainability.

## 1. Current State Analysis

The `books-api.ts` file is a critical component of the application, responsible for all book-related operations. Its key responsibilities include:

*   **Interfacing with the OpenLibrary API:** Searching for books, fetching book details, and downloading cover images.
*   **Local Database Operations:** Creating, reading, updating, and deleting book records in the local database.
*   **Business Logic:** Implementing complex features like relevance scoring for search results, caching, and handling book folders.
*   **Data Validation and Transformation:** Ensuring that book data is valid and in the correct format before being stored in the database.

While the current implementation is functional, it has several areas that can be improved.

### Key Observations:

*   **Monolithic Structure:** The file has grown into a large, monolithic module with a wide range of responsibilities. This makes it difficult to understand, maintain, and test.
*   **Complex Logic:** The relevance scoring and string similarity algorithms are complex and implemented from scratch. While they are well-intentioned, they could be simplified and improved by using specialized libraries.
*   **Manual Caching:** The caching mechanism is implemented manually using a `Map` and `setTimeout`. This is a common source of bugs and can be replaced with a more robust caching library.
*   **Manual HTTP Requests:** The code uses `axios` to make HTTP requests to the OpenLibrary API. While `axios` is a good library, the code for handling redirects, timeouts, and errors is verbose and can be simplified.
*   **Lack of Separation of Concerns:** The file mixes concerns from different layers of the application, such as API interaction, business logic, and data access.

## 2. Proposed Refactoring Plan

The proposed refactoring plan focuses on improving the structure, performance, and maintainability of the `books-api.ts` file. The key recommendations are:

1.  **Decomposition and Separation of Concerns:**
    *   **`open-library-api.ts`:** Create a new file to encapsulate all interactions with the OpenLibrary API. This will include functions for searching books, fetching book details, and downloading cover images. This will also include the `axios` configuration and error handling for the API.
    *   **`book-scoring.ts`:** Move the relevance scoring and string similarity algorithms to a separate file. This will make the logic easier to test and maintain.
    *   **`book-cache.ts`:** Replace the manual caching mechanism with a more robust caching library like `node-cache`. This will simplify the code and reduce the risk of bugs.

2.  **Library Adoption:**
    *   **`fuse.js` for Fuzzy Searching:** Replace the custom string similarity and relevance scoring algorithms with `fuse.js`. Fuse.js is a powerful, lightweight fuzzy-search library that is highly configurable and performant.
    *   **`node-cache` for Caching:** Replace the manual caching implementation with `node-cache`. `node-cache` is a simple and fast caching module for Node.js that supports TTL (time-to-live) and other advanced features.
    *   **`got` for HTTP Requests:** While `axios` is a good library, `got` is a more modern and powerful HTTP request library for Node.js. It has a more streamlined API and better support for features like retries, timeouts, and hooks.

3.  **Code Simplification and Optimization:**
    *   **Simplify `downloadCoverImageData`:** The `downloadCoverImageData` function can be simplified by using the retry and timeout features of `got`.
    *   **Simplify `calculateEnhancedRelevanceScore`:** The `calculateEnhancedRelevanceScore` function can be replaced with a much simpler implementation that uses `fuse.js`.
    *   **Simplify `searchBooksOnline`:** The `searchBooksOnline` function can be simplified by using the new `open-library-api.ts` module and the `book-cache.ts` module.

## 3. Detailed Refactoring Steps

### Step 1: Create `RefactorDocs/open-library-api.ts`

This file will be responsible for all interactions with the OpenLibrary API.

```typescript
// RefactorDocs/open-library-api.ts
import got from 'got';
import { BookSearchResult, OpenLibraryWorkDetails, OpenLibraryAuthorDetails, OpenLibraryEditionDetails } from './types'; // Assuming types are moved to a separate file

const openLibraryClient = got.extend({
    prefixUrl: 'https://openlibrary.org',
    timeout: {
        request: 25000,
    },
    headers: {
        'User-Agent': 'Noti/1.0 (https://github.com/noti-app/noti)',
    },
    retry: {
        limit: 2,
        methods: ['GET'],
    },
});

export const searchBooksByISBN = async (isbn: string): Promise<BookSearchResult[]> => {
    // ... implementation using openLibraryClient
};

export const searchBooks = async (query: string, limit: number = 10): Promise<BookSearchResult[]> => {
    // ... implementation using openLibraryClient
};

export const getBookDetails = async (olid: string): Promise<Partial<Book>> => {
    // ... implementation using openLibraryClient
};

export const downloadCoverImage = async (coverUrl: string): Promise<Buffer> => {
    const response = await got(coverUrl, {
        responseType: 'buffer',
        timeout: 30000,
        retry: {
            limit: 3,
        },
    });
    return response.body;
};
```

### Step 2: Create `RefactorDocs/book-scoring.ts`

This file will be responsible for the relevance scoring and string similarity algorithms.

```typescript
// RefactorDocs/book-scoring.ts
import Fuse from 'fuse.js';
import { BookSearchResult, Book } from './types';

export const calculateRelevance = (results: BookSearchResult[], query: string, localBooks: Book[]): BookSearchResult[] => {
    const fuse = new Fuse(results, {
        keys: ['title', 'author_name'],
        includeScore: true,
        threshold: 0.4,
    });

    const scoredResults = fuse.search(query).map(result => ({
        ...result.item,
        relevanceScore: (1 - (result.score ?? 1)) * 100, // Convert Fuse.js score to a 0-100 scale
    }));

    // Further penalize books already in the local library
    return scoredResults.map(book => {
        const isInLocalLibrary = localBooks.some(localBook =>
            (book.isbn && localBook.isbn && localBook.isbn === book.isbn[0]) ||
            (book.key && localBook.olid && localBook.olid === book.key.split('/').pop())
        );
        if (isInLocalLibrary) {
            book.relevanceScore = (book.relevanceScore ?? 0) - 50;
        }
        return book;
    });
};
```

### Step 3: Create `RefactorDocs/book-cache.ts`

This file will be responsible for caching search results.

```typescript
// RefactorDocs/book-cache.ts
import NodeCache from 'node-cache';
import { BookSearchResult } from './types';

const searchCache = new NodeCache({
    stdTTL: 5 * 60, // 5 minutes
    checkperiod: 60,
});

export const getCachedSearchResults = (key: string): BookSearchResult[] | undefined => {
    return searchCache.get(key);
};

export const setCachedSearchResults = (key: string, results: BookSearchResult[]) => {
    searchCache.set(key, results);
};
```

### Step 4: Refactor `books-api.ts`

After creating the new files, the `books-api.ts` file can be refactored to use them.

```typescript
// electron/main/api/books-api.ts (Refactored)
import * as OpenLibraryAPI from './open-library-api';
import * as BookScoring from './book-scoring';
import * as BookCache from './book-cache';
import { createBook, getAllBooks, ... } from '../database/database-api';
// ... other imports

export const searchBooksOnline = async (
  query: string,
  limit: number = 10,
  localBooks: Book[] = []
): Promise<BookSearchResult[]> => {
    const cacheKey = `${query}_${limit}_${localBooks.length}`;
    const cachedResults = BookCache.getCachedSearchResults(cacheKey);
    if (cachedResults) {
        return cachedResults;
    }

    const onlineResults = await OpenLibraryAPI.searchBooks(query, limit);
    const scoredResults = BookScoring.calculateRelevance(onlineResults, query, localBooks);

    BookCache.setCachedSearchResults(cacheKey, scoredResults);

    return scoredResults;
};

// ... other functions refactored to use the new modules
```

## 4. Benefits of Refactoring

The proposed refactoring will provide several benefits:

*   **Improved Maintainability:** The code will be easier to understand, modify, and test due to the separation of concerns and the use of specialized libraries.
*   **Improved Performance:** The use of `fuse.js` for fuzzy searching and `node-cache` for caching will likely improve the performance of the search functionality.
*   **Reduced Code Complexity:** The code will be simpler and more concise, as the complex logic for relevance scoring, caching, and HTTP requests will be handled by external libraries.
*   **Better Developer Experience:** The refactored code will be easier to work with, as it will be more organized and follow best practices for software design.

By investing in this refactoring effort, the development team can improve the quality of the codebase and make it easier to build new features in the future.
