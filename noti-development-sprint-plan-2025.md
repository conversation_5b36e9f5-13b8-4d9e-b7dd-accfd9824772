# Noti Development Sprint Plan 2025
**Timeline: July 29 - September 17, 2025 (7 weeks, 49 days)**

## Executive Summary

This comprehensive development plan outlines the enhancement of Noti from its current state as a sophisticated note-taking and book management application to a fully-featured study companion with EPUB reading, AI integration, grade calculation, and calendar functionality.

## Current State Analysis

### Existing Codebase Strengths
- **Solid Architecture**: Well-structured Electron + Vue 3 + TypeScript application
- **Rich Database Schema**: 15 interconnected tables supporting complex relationships
- **Mature Features**: Note-taking with TipTap editor, book management with OpenLibrary integration
- **Theme System**: Comprehensive CSS custom properties system (558 lines of theme variables)
- **Performance**: Optimized with component preloading and efficient state management
- **Component Organization**: 50+ Vue components organized by feature (books, notes, timer, etc.)
- **API Layer**: Robust IPC communication with type-safe database operations

### Technical Debt & Improvement Areas Identified
- **CSS Organization**: Inline styles mixed with component logic across 50+ components
- **Component Size**: Large components like NotesView.vue (1000+ lines) need decomposition
- **Library Versions**: Dependencies from 2024 need updates (Vue 3.4.21, Electron 29.1.1)
- **Code Duplication**: Repeated modal patterns, similar API calls across components
- **UI Inconsistency**: Different button styles, spacing, and layout patterns across views
- **Unused Dependencies**: Several packages in package.json may not be actively used
- **File Organization**: Some utility functions could be better organized into libraries

## Detailed Sprint Breakdown

### Sprint 1: Foundation & Refactoring (July 29 - August 12, 2025)
**Duration**: 14 days | **Sprint Goal**: Clean and optimize existing codebase

#### Week 1 (July 29 - August 5)
**Sprint 1.1: Codebase Audit & Cleanup**

**Day 1-2 (July 29-30): Codebase Analysis**
- Audit all 50+ components for unused code and dependencies
- Identify duplicate functions across API modules (notes-api.ts, books-api.ts, etc.)
- Map component dependencies and identify refactoring opportunities
- Document current CSS usage patterns across components

**Day 3-4 (July 31 - August 1): File & Dependency Cleanup**
- Remove unused assets from /public and /src/assets directories
- Clean up package.json dependencies (currently 42 dependencies + 14 devDependencies)
- Remove deprecated components and unused utility functions
- Consolidate duplicate API functions into shared utilities

**Day 5-7 (August 2-5): Component Decomposition**
- Break down NotesView.vue (currently 1000+ lines) into smaller components
- Decompose BooksView.vue and other large view components
- Extract reusable logic into composables
- Create shared component patterns for modals (currently 20+ modal components)

#### Week 2 (August 5 - August 12)
**Sprint 1.2: CSS Architecture & Library Updates**

**Day 8-10 (August 5-7): CSS Architecture Overhaul**
- Extract inline styles from all Vue components
- Create CSS modules for each component category (books/, notes/, timer/, etc.)
- Establish design tokens based on existing theme system (558 CSS variables)
- Implement CSS naming conventions (BEM or similar)

**Day 11-12 (August 8-9): Library Upgrades**
- Update Vue from 3.4.21 to latest stable (3.4.x)
- Update Electron from 29.1.1 to latest stable (31.x)
- Update TipTap ecosystem (@tiptap/* packages from 2.12.0 to latest)
- Update TypeScript, Vite, and build tools

**Day 13-14 (August 10-12): Integration Testing**
- Test all existing functionality after refactoring
- Fix any breaking changes from library updates
- Performance testing to ensure no regressions
- Documentation updates for new code structure

### Sprint 2: UI Consistency & Design System (August 12 - August 19, 2025)
**Duration**: 7 days | **Sprint Goal**: Establish consistent UI patterns across all views

**Day 15-16 (August 12-13): Design System Foundation**
- Create design system documentation based on existing theme variables
- Establish component library structure (/src/components/ui/)
- Define spacing scale, typography scale, and color palette standards
- Create Figma/design mockups for consistent component patterns

**Day 17-18 (August 14-15): Core UI Components**
- Build reusable Button component with variants (primary, secondary, outlined)
- Create Input component family (text, textarea, select, checkbox)
- Develop Card component with consistent shadows and borders
- Implement Modal base component to replace 20+ existing modal variations

**Day 19-20 (August 16-17): Layout Standardization**
- Standardize page headers across all 6 main views (Notes, Books, Folders, Timer, Dashboard, Settings)
- Update SidebarNavigation.vue for consistent navigation patterns
- Implement consistent spacing and layout grids
- Create responsive breakpoint system

**Day 21 (August 18-19): View Updates & Testing**
- Apply new design system to all existing views
- Update all modal components to use new base Modal component
- Cross-browser testing for UI consistency
- Accessibility audit and improvements

### Sprint 3: EPUB Reader Implementation (August 19 - September 2, 2025)
**Duration**: 14 days | **Sprint Goal**: Complete EPUB reading functionality with annotations

#### Week 4 (August 19 - August 26)
**Sprint 3.1: EPUB Processing Engine**

**Day 22-23 (August 19-20): EPUB Infrastructure**
- Research and select EPUB processing library (epub.js vs custom implementation)
- Set up EPUB file handling in Electron main process
- Create database schema extensions for EPUB metadata and reading progress
- Implement file import functionality for .epub files

**Day 24-25 (August 21-22): Content Parsing & Extraction**
- Implement EPUB file extraction and content parsing
- Extract metadata (title, author, chapters, table of contents, cover)
- Parse HTML/CSS content for proper rendering
- Handle different EPUB versions (2.0, 3.0, 3.1)

**Day 26-28 (August 23-26): Basic Reader Interface**
- Create EPUBReader.vue component with basic display functionality
- Implement chapter navigation and table of contents
- Add basic reading controls (font size, theme, page turning)
- Integrate with existing book management system

#### Week 5 (August 26 - September 2)
**Sprint 3.2: Advanced Reading Features**

**Day 29-30 (August 26-27): Annotation System**
- Implement text selection and highlighting functionality
- Create annotation database schema (highlights, notes, bookmarks)
- Build annotation UI components (highlight colors, note editor)
- Add annotation management (edit, delete, search annotations)

**Day 31-32 (August 28-29): Reading Progress & Settings**
- Implement reading position tracking and persistence
- Add reading time tracking and statistics
- Create reading settings panel (font, theme, margins, line spacing)
- Build progress visualization (percentage, pages, time estimates)

**Day 33-35 (August 30 - September 2): Integration & Polish**
- Connect EPUB reader with existing Notes system for annotation notes
- Implement search functionality within EPUB content
- Add export functionality for annotations and notes
- Performance optimization for large EPUB files

### Sprint 4: AI Integration & RAG System (August 26 - September 9, 2025)
**Duration**: 14 days | **Sprint Goal**: Implement AI-powered features with RAG capability

#### Week 5 Overlap (August 26 - September 2)
**Sprint 4.1: AI Infrastructure Setup**

**Day 29-30 (August 26-27): AI Service Architecture**
- Design AI service architecture with provider abstraction
- Implement BYOK (Bring Your Own Key) system for OpenAI, Anthropic, etc.
- Create AI settings panel in SettingsView.vue
- Set up secure API key storage and management

**Day 31-32 (August 28-29): Vector Database & RAG Setup**
- Choose and integrate vector database (Pinecone, Weaviate, or local solution)
- Implement content indexing for existing notes and book content
- Create embedding generation pipeline for text content
- Build RAG retrieval system for contextual AI responses

#### Week 6 (September 2 - September 9)
**Sprint 4.2: AI Features Implementation**

**Day 36-37 (September 2-3): Web Search Integration**
- Integrate web search API (Google Custom Search, Bing, or DuckDuckGo)
- Implement search result processing and relevance scoring
- Create web search UI components
- Add search result caching and management

**Day 38-39 (September 4-5): Note Enhancement AI Features**
- Implement AI-powered note summarization
- Add note expansion and improvement suggestions
- Create AI formatting and structure recommendations
- Build note-to-note connection suggestions using RAG

**Day 40-42 (September 6-9): Book Analysis & Study Tools**
- Implement AI book analysis (summaries, themes, character analysis)
- Create discussion question generation for books
- Build quiz and flashcard generation from notes and book content
- Add semantic search across all content (notes, books, annotations)

**Day 42 (September 9): AI Integration Testing**
- Test all AI features with different providers
- Performance testing for RAG queries
- User experience testing for AI feature discoverability

### Sprint 5: Grade Calculator & Calendar (September 2 - September 17, 2025)
**Duration**: 15 days | **Sprint Goal**: Add comprehensive academic tracking similar to Datz

#### Week 6 Overlap (September 2 - September 9)
**Sprint 5.1: Grade Calculation System**

**Day 36-37 (September 2-3): Database Schema & Course Management**
- Design and implement grade calculation database schema:
  - `courses` table (id, name, credits, semester, year, professor, color)
  - `grade_categories` table (id, course_id, name, weight_percentage)
  - `assignments` table (id, course_id, category_id, name, points_possible, points_earned, due_date, status)
  - `academic_terms` table (id, name, start_date, end_date, is_current)
- Create course management UI (add, edit, delete courses)
- Implement semester/term management system

**Day 38-39 (September 4-5): Assignment Tracking**
- Build assignment management interface
- Implement assignment categories with custom weights
- Add due date tracking and overdue notifications
- Create assignment status tracking (not started, in progress, completed, graded)

**Day 40-42 (September 6-9): GPA Calculation Engine**
- Implement real-time GPA calculation algorithms
- Add what-if scenario calculations (predict GPA with hypothetical grades)
- Create grade scale management (A=4.0, B=3.0, etc. with customization)
- Build cumulative GPA tracking across multiple semesters

#### Week 7 (September 9 - September 17)
**Sprint 5.2: Calendar Integration & Academic Visualization**

**Day 43-44 (September 9-10): Calendar Infrastructure**
- Design calendar database schema:
  - `calendar_events` table (id, title, description, start_time, end_time, type, course_id, recurrence_rule)
  - `class_schedules` table (id, course_id, day_of_week, start_time, end_time, location, recurrence_pattern)
- Implement calendar view component with month/week/day views
- Add recurring event support for class schedules

**Day 45-46 (September 11-12): Schedule Management**
- Build class schedule management interface
- Implement recurring class schedule creation
- Add location and professor information to calendar events
- Create conflict detection for overlapping events

**Day 47-48 (September 13-14): Integration Features**
- Connect assignment due dates with calendar system
- Integrate timer sessions with calendar for study block planning
- Add exam scheduling with preparation time blocking
- Implement grade deadline reminders and notifications

**Day 49 (September 15-17): Academic Analytics & Polish**
- Create grade trend visualization using existing Chart.js integration
- Build academic performance dashboard
- Add semester/term comparison charts
- Implement grade prediction and goal-setting features
- Final testing and bug fixes for grade calculator and calendar

### Sprint 6: Integration & Polish (September 15 - September 17, 2025)
**Duration**: 3 days | **Sprint Goal**: Seamless feature integration and final polish

**Day 47-49 (September 15-17): Final Integration & Testing**

**Cross-Feature Integration:**
- Connect EPUB annotations with Notes system for seamless note-taking
- Link AI-generated study materials with Grade Calculator assignments
- Integrate Calendar events with Timer for automatic study session tracking
- Connect Book reading progress with academic course assignments

**Performance Optimization:**
- Database query optimization for new tables and relationships
- Memory usage optimization for EPUB rendering and AI processing
- UI responsiveness improvements for large datasets
- Bundle size optimization and lazy loading implementation

**Quality Assurance:**
- End-to-end testing of complete workflows (reading → note-taking → AI analysis → grade tracking)
- Cross-platform testing (Windows, macOS, Linux)
- Performance benchmarking against current version
- User acceptance testing with beta users

**Final Polish:**
- UI/UX refinements based on testing feedback
- Documentation updates for new features
- Error handling and edge case coverage
- Accessibility improvements and keyboard navigation

## Technical Implementation Details

### New Component Architecture

#### EPUB Reader Module
```
src/components/epub/
├── EPUBReader.vue           # Main reader component
├── EPUBLibrary.vue          # EPUB file management
├── EPUBAnnotations.vue      # Annotation system
├── EPUBNavigation.vue       # Chapter/TOC navigation
├── EPUBSettings.vue         # Reading preferences
└── EPUBProgress.vue         # Reading progress tracking

electron/main/api/
├── epub-api.ts              # EPUB processing API
└── epub-parser.ts           # File parsing utilities
```

#### AI Integration Module
```
src/components/ai/
├── AIAssistant.vue          # Main AI interface
├── AISettings.vue           # Provider configuration
├── AIPromptBuilder.vue      # Custom prompt creation
└── AIResults.vue            # AI response display

electron/main/api/ai/
├── ai-providers.ts          # OpenAI, Anthropic, etc.
├── rag-system.ts           # Vector database integration
├── web-search.ts           # Search API integration
└── prompt-templates.ts     # Predefined prompts
```

#### Grade Calculator Module
```
src/components/grades/
├── GradeCalculator.vue      # Main calculator interface
├── CourseManager.vue        # Course CRUD operations
├── AssignmentTracker.vue    # Assignment management
├── GPADashboard.vue         # GPA visualization
└── GradeAnalytics.vue       # Performance charts

src/views/
└── GradesView.vue           # Main grades page
```

#### Calendar Module
```
src/components/calendar/
├── CalendarView.vue         # Main calendar interface
├── EventCreator.vue         # Event creation modal
├── ClassScheduler.vue       # Recurring class setup
└── CalendarIntegration.vue  # Cross-feature connections

Database Schema Extensions:
- courses, assignments, grade_categories, academic_terms
- calendar_events, class_schedules, recurring_patterns
```

### Database Schema Extensions

#### New Tables for Grade Calculator
```sql
CREATE TABLE courses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    code TEXT,
    credits INTEGER DEFAULT 3,
    semester TEXT NOT NULL,
    year INTEGER NOT NULL,
    professor TEXT,
    color TEXT DEFAULT '#333333',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE grade_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    course_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    weight_percentage REAL NOT NULL,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
);

CREATE TABLE assignments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    course_id INTEGER NOT NULL,
    category_id INTEGER,
    name TEXT NOT NULL,
    description TEXT,
    points_possible REAL NOT NULL,
    points_earned REAL,
    due_date TIMESTAMP,
    status TEXT DEFAULT 'not_started',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES grade_categories(id) ON DELETE SET NULL
);

CREATE TABLE academic_terms (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_current BOOLEAN DEFAULT FALSE
);
```

#### New Tables for Calendar System
```sql
CREATE TABLE calendar_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    description TEXT,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    event_type TEXT DEFAULT 'general',
    course_id INTEGER,
    assignment_id INTEGER,
    location TEXT,
    recurrence_rule TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE
);

CREATE TABLE class_schedules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    course_id INTEGER NOT NULL,
    day_of_week INTEGER NOT NULL, -- 0=Sunday, 1=Monday, etc.
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    location TEXT,
    professor TEXT,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
);
```

#### EPUB and AI Extensions
```sql
CREATE TABLE epub_books (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    chapter_count INTEGER,
    word_count INTEGER,
    reading_position TEXT, -- JSON: {chapter, position}
    reading_time_minutes INTEGER DEFAULT 0,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

CREATE TABLE epub_annotations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    epub_book_id INTEGER NOT NULL,
    chapter_id TEXT NOT NULL,
    start_position INTEGER NOT NULL,
    end_position INTEGER NOT NULL,
    selected_text TEXT NOT NULL,
    annotation_text TEXT,
    highlight_color TEXT DEFAULT '#ffff00',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (epub_book_id) REFERENCES epub_books(id) ON DELETE CASCADE
);

CREATE TABLE ai_interactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_prompt TEXT NOT NULL,
    ai_response TEXT NOT NULL,
    provider TEXT NOT NULL,
    model TEXT,
    context_type TEXT, -- 'note', 'book', 'general'
    context_id INTEGER,
    tokens_used INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Detailed Task Breakdown by Priority

### Critical Path Items (Must Complete)
1. **Codebase Refactoring** (Days 1-14) - Foundation for all other work
2. **UI Consistency** (Days 15-21) - Required for professional appearance
3. **EPUB Reader Core** (Days 22-35) - Primary new feature
4. **Grade Calculator** (Days 36-49) - Essential academic functionality

### High Priority Items
1. **AI Integration Basic** (Days 29-42) - Core AI features
2. **Calendar Integration** (Days 43-49) - Academic scheduling
3. **Feature Integration** (Days 47-49) - Seamless user experience

### Medium Priority Items (Nice to Have)
1. **Advanced AI Features** - Enhanced RAG, complex analysis
2. **Advanced Calendar Features** - Complex recurring patterns
3. **Performance Optimizations** - Beyond basic requirements

## Risk Assessment & Mitigation Strategies

### High-Risk Items (Probability: High, Impact: High)
1. **EPUB Rendering Complexity**
   - **Risk**: Complex EPUB files may not render correctly
   - **Mitigation**: Use proven library (epub.js), extensive testing with various EPUB formats
   - **Contingency**: Fallback to basic text extraction if full rendering fails

2. **AI API Costs & Rate Limits**
   - **Risk**: Unexpected high costs or API limitations
   - **Mitigation**: Implement usage tracking, rate limiting, cost alerts
   - **Contingency**: Graceful degradation to non-AI features

3. **Performance Impact from New Features**
   - **Risk**: App becomes slow with EPUB rendering and AI processing
   - **Mitigation**: Lazy loading, background processing, performance monitoring
   - **Contingency**: Feature flags to disable heavy features if needed

### Medium-Risk Items (Probability: Medium, Impact: Medium)
1. **Library Compatibility Issues**
   - **Risk**: Updated dependencies break existing functionality
   - **Mitigation**: Staged updates, comprehensive testing, version pinning
   - **Contingency**: Rollback plan for each library update

2. **Database Migration Complexity**
   - **Risk**: New schema changes break existing data
   - **Mitigation**: Database backup before changes, migration scripts, testing
   - **Contingency**: Database rollback procedures

3. **Feature Integration Complexity**
   - **Risk**: New features don't integrate well with existing ones
   - **Mitigation**: Incremental integration, interface design, testing
   - **Contingency**: Feature isolation if integration fails

## Success Metrics & KPIs

### Technical Quality Metrics
- **Code Quality**: Reduce cyclomatic complexity by 30% (current large components)
- **Performance**: Maintain <2s app startup time despite new features
- **Test Coverage**: Achieve 80% code coverage for new features
- **Bundle Size**: Keep increase under 25% despite significant new functionality
- **Memory Usage**: No more than 50% increase in peak memory usage

### Feature Completion Metrics
- **Refactoring**: 100% of identified large components decomposed
- **UI Consistency**: 100% of views follow new design system
- **EPUB Support**: Support for 95% of standard EPUB files
- **AI Integration**: All planned AI features functional with at least 2 providers
- **Grade Calculator**: Full GPA calculation with what-if scenarios
- **Calendar**: Complete academic scheduling with assignment integration

### User Experience Metrics
- **Feature Discoverability**: New features accessible within 3 clicks
- **Performance**: No degradation in existing feature response times
- **Error Handling**: Graceful error messages for all failure scenarios
- **Accessibility**: WCAG 2.1 AA compliance for all new components

## Resource Requirements & Dependencies

### Development Environment
- **Hardware**: Development machine with 16GB+ RAM for Electron development
- **Software**: Node.js 18+, latest stable versions of development tools
- **Design Tools**: Figma for UI mockups and design system documentation
- **Testing**: Jest for unit tests, Playwright for E2E testing

### External Services & APIs
- **AI Providers**: OpenAI API, Anthropic Claude API (BYOK support)
- **Vector Database**: Pinecone (cloud) or Weaviate (self-hosted) for RAG
- **Web Search**: Google Custom Search API or Bing Search API
- **EPUB Processing**: epub.js library or equivalent

### New Dependencies to Add
```json
{
  "epub.js": "^0.3.93",
  "@pinecone-database/pinecone": "^1.1.0",
  "openai": "^4.20.0",
  "@anthropic-ai/sdk": "^0.9.0",
  "date-fns": "^2.30.0",
  "recharts": "^2.8.0",
  "react-calendar": "^4.6.0"
}
```

## Timeline Summary

**Total Duration**: 49 days (7 weeks)
**Working Days**: Assuming 7 days/week development schedule
**Milestones**:
- **Week 2 End**: Refactored codebase with updated libraries
- **Week 3 End**: Consistent UI across all views
- **Week 5 End**: Functional EPUB reader with annotations
- **Week 6 End**: AI integration with basic RAG functionality
- **Week 7 End**: Complete grade calculator and calendar system
- **Final Day**: Fully integrated study companion application

This comprehensive plan transforms Noti from a note-taking app into a complete academic companion while maintaining code quality and user experience standards. The phased approach allows for iterative development and testing, reducing risks while ensuring all features work together seamlessly.
